<template>
  <view class="travel-period-wrapper">
    <view class="travel-period-calendar-wrapper">
      <DatePicker :date="startDate" @date-change="handleStartDateChange" :title="'确认出发时间'" />
    </view>
    <view class="travel-period-duration">{{ diff }}天</view>
    <view class="travel-period-calendar-wrapper">
      <DatePicker :date="endDate" :is-end="true" @date-change="handleEndDateChange" :base-date="startDate" :min-date="startDate" :title="'确认结束时间'" />
    </view>
  </view>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import dayjs from 'dayjs';
  import DatePicker from './datePicker.vue';

  const props = defineProps({
    onUpdate: { type: Function, required: true },
  });

  const DEFAULT_GAP = 2;

  // 日期状态
  const startDate = ref(dayjs().format('YYYY-MM-DD'));
  const endDate = ref(dayjs().add(DEFAULT_GAP, 'days').format('YYYY-MM-DD'));

  // 计算属性
  const diff = computed(() => {
    const start = dayjs(startDate.value);
    const end = dayjs(endDate.value);
    return end.diff(start, 'day') + 1;
  });

  // 方法
  const handleStartDateChange = (e) => {
    const newStart = e;
    startDate.value = newStart;
    // 调整结束日期
    const start = dayjs(newStart);
    const currentEnd = dayjs(endDate.value);

    if (currentEnd.isBefore(start) || currentEnd.diff(start, 'day') + 1 > 7) {
      endDate.value = start.add(DEFAULT_GAP, 'days').format('YYYY-MM-DD');
    }
  };

  const handleEndDateChange = (e) => {
    endDate.value = e;
  };

  // 监听日期变化更新父组件
  watch(
    [startDate, endDate],
    () => {
      const start = dayjs(startDate.value);
      const formatStr = start.year() !== dayjs().year() ? 'YYYY年M月D日' : 'M月D日';
      const displayText = `${start.format(formatStr)}我想到xxx玩${diff.value}天`;
      props.onUpdate(displayText);
    },
    { immediate: true },
  );
</script>

<style scoped lang="scss">
  .travel-period-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    height: 112rpx;
  }

  .travel-period-calendar-wrapper {
    flex: 1;
  }

  .travel-period-duration {
    padding: 0 20rpx;
    text-align: center;
  }
</style>
