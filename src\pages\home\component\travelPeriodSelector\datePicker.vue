<template>
  <view class="custom-calendarPicker">
    <uni-datetime-picker v-model="dateValue" type="date" :title="title" :start="minDate" :end="maxDate" :show="show" @change="handleConfirm" @maskClick="handleClose">
      <view @click="show = true" class="text-wrapper" :class="isEnd ? 'text-wrapper__end' : ''">
        <view class="date">{{ formattedDate }}</view>
        <view class="weekDay">{{ weekDay }}</view>
      </view>
    </uni-datetime-picker>
  </view>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue';
  import dayjs from 'dayjs';
  import { getMinDate } from '../strategyCustomization/config';

  const props = defineProps<{
    title?: string;
    date: Date | null;
    onDateChange?: (date: Date | null) => void;
    isEnd?: boolean;
    minDate?: Date;
    baseDate?: Date;
  }>();

  const emit = defineEmits<{
    (e: 'date-change', date: Date | null): void;
  }>();

  // 状态管理
  const show = ref(false);
  const dateValue = ref<string>(props.date ? dayjs(props.date).format('YYYY-MM-DD') : '');

  // 计算属性
  const minDate = computed(() => (props.minDate ? dayjs(props.minDate).format('YYYY-MM-DD') : dayjs(getMinDate()).format('YYYY-MM-DD')));
  const maxDate = computed(() => {
    if (!props.baseDate) return '';
    return dayjs(props.baseDate).add(6, 'day').format('YYYY-MM-DD');
  });
  // 修改计算属性
  const formattedDate = computed(() => {
    if (!dateValue.value) return '';
    return `${dayjs(dateValue.value).month() + 1}月${dayjs(dateValue.value).date()}日`;
  });

  const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const weekDay = computed(() => {
    if (!dateValue.value) return '';
    return weekDays[dayjs(dateValue.value).day()];
  });

  // 监听props变化
  watch(
    () => props.date,
    (newVal) => {
      dateValue.value = newVal ? dayjs(newVal).format('YYYY-MM-DD') : '';
    },
    { immediate: true },
  );

  // 事件处理
  const handleClose = () => {
    show.value = false;
  };

  // 添加handleConfirm实现
  const handleConfirm = () => {
    const newVal = dayjs(dateValue.value).startOf('day'); // 选择的日期默认按照0点，避免影响时间戳判断
    dateValue.value = newVal.format('YYYY-MM-DD');
    props.onDateChange?.(dateValue.value ? newVal.toDate() : null);
    show.value = false;
  };
</script>

<style scoped lang="scss">
  .custom-calendarPicker {
    .text-wrapper {
      display: flex;
      align-items: center;
      &__end {
        justify-content: flex-end;
      }
    }

    .date {
      height: 38rpx;
      font-weight: 500;
      font-size: 40rpx;
      color: #191919;
      line-height: 38rpx;
      text-align: left;
      margin-right: 8rpx;
    }

    .weekDay {
      height: 28rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #191919;
      line-height: 28rpx;
      text-align: left;
    }
  }
</style>
