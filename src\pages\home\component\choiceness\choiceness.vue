<template>
  <div class="grid">
    <div v-for="(card, index) in cards" :key="index" :class="['card', index === 4 ? 'cardSpan2' : '']" @click="() => onChoose?.({ id: card.id, userId: card.uid })">
      <!-- :src="imageLinkMapToID[card.id] || card.places_pic" -->
      <img :src="imageLinkMapToID[card.id] || card.places_pic" :alt="card.title" :class="['image']" loading="lazy" style="object-fit: cover" />
      <div :class="['overlay']">
        <h3 :class="['title']">
          <div>{{ card.title }}</div>
        </h3>
        <div :class="['footer']">
          <span :class="['location']">
            <!-- <LocationFill style="color: #ffffff" /> -->
            {{ card.plan_list }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps } from 'vue';
  // import { LocationFill } from 'antd-mobile-icons';
  import { imageLinkMapToID } from './config';

  type CardItem = {
    id: number;
    title: string;
    places_pic: string;
    plan_list: string;
    uid: string;
  };

  const props = defineProps<{
    cards: CardItem[];
    onChoose?: (field: { id: number; userId: string }) => void;
  }>();
</script>

<style scoped lang="scss">
  .grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr); // 三列等宽
    gap: 10px;
    .card {
      position: relative;
      height: 150px;
      border-radius: 8px;
      overflow: hidden;
      transition: transform 0.2s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

      // &:hover {
      //   transform: translateY(-4px);
      // }
    }
    .cardSpan2 {
      grid-column: span 2;
    }

    .image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .overlay {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent 60%);
      color: #fff;
      // padding: 9px;
      box-sizing: border-box;
      font-weight: 500;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .title {
      padding: 9px 9px 2px;
      margin-top: 0;
      margin-bottom: 0;
      background: linear-gradient(359deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 32%, rgba(0, 0, 0, 0.54) 100%);
      > div {
        font-size: 14px;
        font-weight: bold;
        overflow: hidden; /* 隐藏超出部分 */
        display: -webkit-box; /* 使用弹性盒子模型 */
        -webkit-line-clamp: 2; /* 限制显示两行 */
        -webkit-box-orient: vertical; /* 设置内容方向为垂直 */
        text-overflow: ellipsis; /* 设置省略号 */
      }
    }

    .footer {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      align-items: center;
      opacity: 0.9;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 32%, rgba(0, 0, 0, 0.54) 100%);
    }

    .location {
      overflow: hidden; /* 隐藏超出部分 */
      display: -webkit-box; /* 使用弹性盒子模型 */
      -webkit-box-orient: vertical; /* 设置内容方向为垂直 */
      -webkit-line-clamp: 2; /* 限制显示两行 */
      text-overflow: ellipsis; /* 设置省略号 */
      margin: 9px;
    }

    .source {
      white-space: nowrap;
    }
  }
</style>
