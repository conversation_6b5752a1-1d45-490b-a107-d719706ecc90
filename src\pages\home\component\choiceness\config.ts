import Hangzhou from './mock-imgs/hangzhou.png';
import Beijing from './mock-imgs/beijing.png';
import Guangzhou from './mock-imgs/guangzhou.png';
import Chengdu from './mock-imgs/chengdu.png';
import Shanghai from './mock-imgs/shanghai.png';
import Shenzhen from './mock-imgs/shenzhen.png';
import Kunming from './mock-imgs/kunming.png';
import Harbin from './mock-imgs/harbin.png';

export enum ImageIDEnum {
  '杭州' = '892',
  '北京' = '893',
  '广州' = '894',
  '成都' = '895',
  '上海' = '896',
  '深圳' = '899',
  '昆明' = '897',
  '哈尔滨' = '898',
}

// 首页使用默认图片，和项目一起打包，确保首页迅速展示图片
export const imageLinkMapToID = {
  [ImageIDEnum.杭州]: Hangzhou,
  [ImageIDEnum.北京]: Beijing,
  [ImageIDEnum.广州]: Guangzhou,
  [ImageIDEnum.成都]: Chengdu,
  [ImageIDEnum.上海]: Shanghai,
  [ImageIDEnum.深圳]: Shenzhen,
  [ImageIDEnum.昆明]: Kunming,
  [ImageIDEnum.哈尔滨]: Harbin,
};

export const mockData = [
  {
    id: '892',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '杭州心旅：山水禅意与诗意街巷之约 · 3天 | 1个城市',
    places_pic: 'http://store.is.autonavi.com/showpic/28440e82934e69436f458b567d35fcb5',
    travel_days: 3,
    plan_list: '龙翔桥 → 西湖游船码头 → 三潭印月 → 花港观鱼',
    num_places: 22,
  },
  {
    id: '893',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '京城印记：宫墙烟雨与山河宏图 · 5天 | 1个城市',
    places_pic: 'http://store.is.autonavi.com/showpic/2f968490d105bb2741e17f90b85c6b79',
    travel_days: 5,
    plan_list: '南锣鼓巷 → 前门大街 → 王府井步行街',
    num_places: 21,
  },
  {
    id: '894',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '南国浮光·岭南文脉穿梭记 · 5天 | 1个城市',
    places_pic: 'https://store.is.autonavi.com/showpic/2c0f1bf5dee78abbd9956eafd2a987e8',
    travel_days: 5,
    plan_list: '石室圣心大教堂 → 沙面 → 恩宁路骑楼街',
    num_places: 19,
  },
  {
    id: '895',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '诗韵流芳·熊猫故里漫游记 · 3天 | 1个城市（成都）',
    places_pic: 'https://aos-comment.amap.com/B0FFGQ5RIP/comment/content_media_external_images_media_4729_ss__1731581389709_61352293.jpg',
    travel_days: 3,
    plan_list: '成都武侯祠博物馆 → 锦里古街',
    num_places: 13,
  },
  {
    id: '896',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '摩登与古典的交汇：上海人文漫游记 · 4天 | 1个城市',
    places_pic: 'https://store.is.autonavi.com/showpic/0ed6a4415156ded208b6b8830970191d',
    travel_days: 4,
    plan_list: '上海迪士尼度假区 → 豫园 → 老城厢九曲桥',
    num_places: 12,
  },
  {
    id: '897',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '春城漫游·昆明两日深度体验',
    places_pic: 'https://store.is.autonavi.com/showpic/8bdb2fb845c1cfc9a2b44353e9208a0c',
    travel_days: 2,
    plan_list: '滇池 → 云南民族村 → 公园1903',
    num_places: 9,
  },
  {
    id: '898',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '北国乐章·哈尔滨城市巡礼 3天 | 1个城市',
    places_pic: 'https://store.is.autonavi.com/showpic/9017b04f94476397a6d48ddb8ca75b5a',
    travel_days: 3,
    plan_list: '中央大街步行街 → 防洪纪念塔 → 斯大林公园',
    num_places: 11,
  },
  {
    id: '899',
    uid: 'recommand',
    start_date: null,
    end_date: null,
    title: '浪漫深圳·山海古韵48小时 2天 | 1个城市',
    places_pic: 'https://store.is.autonavi.com/showpic/cf53b9fe429fd87a3ca3c714fd180f3c',
    travel_days: 2,
    plan_list: '大鹏古城 → 较场尾海滩 → 桔钓沙',
    num_places: 7,
  },
];
